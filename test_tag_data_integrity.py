#!/usr/bin/env python3
"""
Tag Configuration Data Integrity Test for Adventure Chess Creator

This script tests each of the 28 tag configurations individually to identify
specific data integrity issues in their populate_data/collect_data methods.
It creates test data for each tag and verifies round-trip integrity.

Usage:
    python test_tag_data_integrity.py
    python test_tag_data_integrity.py --tag range
    python test_tag_data_integrity.py --verbose
"""

import sys
import os
import json
import logging
from typing import Dict, Any, List, Tuple, Optional
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from editors.ability_editor.tag_configs import TAG_CONFIG_REGISTRY
from editors.ability_editor.ability_editor_main import AbilityEditorWindow

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)


class TagDataIntegrityTester:
    """Test data integrity for individual tag configurations."""
    
    def __init__(self):
        """Initialize the tester with a minimal editor instance."""
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.editor = None
        self.test_results = {}
        
    def setup_editor(self):
        """Create a minimal editor instance for testing."""
        try:
            self.editor = AbilityEditorWindow()
            print("✓ Created AbilityEditorWindow for testing")
        except Exception as e:
            print(f"❌ Failed to create editor: {e}")
            return False
        return True

    def create_tag_ui_for_testing(self, tag_name: str, config_instance) -> bool:
        """
        Create UI widgets for a tag configuration to enable proper testing.

        Args:
            tag_name: Name of the tag being tested
            config_instance: Instance of the tag configuration class

        Returns:
            True if UI creation succeeded, False otherwise
        """
        try:
            # Create a test widget container
            test_widget = QWidget()
            test_layout = QVBoxLayout()
            test_widget.setLayout(test_layout)

            # Call the tag's create_ui method to instantiate all widgets
            config_instance.create_ui(test_layout)

            # Store the test widget so it doesn't get garbage collected
            self._test_widgets = getattr(self, '_test_widgets', {})
            self._test_widgets[tag_name] = test_widget

            return True

        except Exception as e:
            print(f"  ⚠️ Failed to create UI for {tag_name}: {e}")
            return False
    
    def create_test_data_for_tag(self, tag_name: str) -> Dict[str, Any]:
        """Create comprehensive test data for a specific tag."""
        test_data = {
            "version": "1.0.0",
            "name": f"Test {tag_name.title()} Ability",
            "description": f"Test ability for {tag_name} tag",
            "cost": 2,
            "tags": [tag_name],
            "activationMode": "click",
            "autoCostCheck": True
        }
        
        # Add tag-specific test data based on known patterns
        tag_specific_data = self._get_tag_specific_test_data(tag_name)
        test_data.update(tag_specific_data)
        
        return test_data
    
    def _get_tag_specific_test_data(self, tag_name: str) -> Dict[str, Any]:
        """Generate realistic test data for specific tag types."""
        data = {}
        
        if tag_name == "range":
            data.update({
                "rangePattern": [[True, False, True], [False, True, False], [True, False, True]],
                "rangePiecePosition": [1, 1],
                "rangeFriendlyOnly": True,
                "rangeEnemyOnly": False,
                "rangeCheckboxStates": [[True, False, True], [False, False, False], [True, False, True]]
            })
        
        elif tag_name == "carryPiece":
            data.update({
                "carryTargets": [{"piece": "Friendly Pawn", "cost": 1}],
                "carryRange": 3,
                "carryShareAbilities": True,
                "carryStartingPiece": True,
                "carryDropOnDeath": True,
                "carryDropMode": "random",
                "carryDropRange": 2
            })
        
        elif tag_name == "swapPlaces":
            data.update({
                "swapTargets": [{"piece": "Friendly Queen", "cost": 2}],
                "swapRange": 5,
                "swapType": "Direct",
                "swapFriendly": True,
                "swapEnemy": False,
                "swapEmpty": True,
                "swapMustTarget": False,
                "swapPreserveAbilities": True,
                "swapDamage": False,
                "swapStun": True
            })
        
        elif tag_name == "summon":
            data.update({
                "summonTargets": [{"piece": "Friendly Pawn", "cost": 1}, {"piece": "Friendly Knight", "cost": 3}],
                "summonMax": 4
            })
        
        elif tag_name == "addObstacle":
            data.update({
                "obstacleType": "wall"
            })

        elif tag_name == "adjacencyRequired":
            data.update({
                "adjacencyTilePattern": [[False, False, False], [False, False, False], [False, False, False]]
            })

        elif tag_name == "areaEffect":
            data.update({
                "areaEffectSize": 1,
                "areaEffectShape": "Circle",
                "areaEffectTarget": [2, 3],
                "areaEffectCenter": [4, 4]
            })

        elif tag_name == "buffPiece":
            data.update({
                "buffTargets": [{"piece": "Friendly Pawn", "cost": 1}],
                "buffDuration": 3,
                "buffOptions": {
                    "attack": True
                }
            })

        elif tag_name == "capture":
            data.update({
                "captureTarget": "Enemy",
                "captureDestroy": True,
                "captureThroughPieces": False,
                "captureMustCapture": False,
                "captureChain": False
            })

        elif tag_name == "convertPiece":
            data.update({
                "convertTargets": [{"piece": "Enemy Pawn", "cost": 1}]
            })

        elif tag_name == "duplicate":
            data.update({
                "duplicatePositionsMap": [[False for _ in range(8)] for _ in range(8)]
            })

        elif tag_name == "debuffPiece":
            data.update({
                "debuffTargets": [{"piece": "Enemy Knight", "cost": 2}],
                "debuffDuration": 2,
                "debuffOptions": {
                    "defense": True
                }
            })

        elif tag_name == "delay":
            data.update({
                "delayTurns": 2
            })

        elif tag_name == "displacePiece":
            data.update({
                "displaceDirection": "North",
                "displaceDistance": 1,
                "displaceForce": False,
                "displacePushThrough": False,
                "displaceCollisionDamage": False,
                "displacePiecePosition": [3, 3]
            })

        elif tag_name == "fogOfWar":
            data.update({
                "fogVisionType": "sight",
                "fogRadius": 2
            })



        elif tag_name == "invisible":
            data.update({
                "invisibleDuration": 0,
                "invisibleCooldown": 0
            })

        elif tag_name == "losRequired":
            data.update({
                "losIgnoreFriendly": True
            })

        elif tag_name == "move":
            data.update({
                "moveDistance": 3,
                "moveType": "normal",
                "movePattern": "custom",
                "moveThroughPieces": False,
                "moveMustFull": False,
                "moveToOccupied": False
            })

        elif tag_name == "noTurnCost":
            data.update({
                "noTurnCostAlways": False,
                "noTurnCostConditional": False,
                "noTurnCostCondition": "On Kill",
                "noTurnCostUses": 1,
                "noTurnCostCooldown": False,
                "noTurnCostStacks": False,
                "noTurnCostSuccessOnly": False
            })

        elif tag_name == "pulseEffect":
            data.update({
                "pulseInterval": 1
            })

        elif tag_name == "reaction":
            data.update({
                "reactionTargets": [{"piece": "Enemy Pawn", "cost": 1}],
                "reactionEvents": ["move", "capture"]
            })

        elif tag_name == "removeObstacle":
            data.update({
                "removeObstacleType": "any"
            })

        elif tag_name == "revival":
            data.update({
                "revivalTargets": [{"piece": "Friendly Bishop", "cost": 2}],
                "revivalMax": 2,
                "revivalHealth": 100,
                "revivalRestoreAbilities": True,
                "revivalReviveEnemies": False
            })

        elif tag_name == "shareSpace":
            data.update({
                "shareSpaceMax": 2,
                "shareSpaceMovement": {"individualMovement": True}
            })

        elif tag_name == "trapTile":
            data.update({
                "trapDuration": 0,
                "trapTriggerLimit": 0,
                "trapVisibility": {"enemies": False, "allies": True}
            })
        

        
        elif tag_name == "immobilize":
            data.update({
                "immobilizeDuration": 2
            })
        

        


        
        return data
    
    def test_tag_configuration(self, tag_name: str) -> Tuple[bool, List[str], Dict[str, Any]]:
        """Test a single tag configuration for data integrity."""
        issues = []
        collected_data = {}

        try:
            # Get the tag configuration class
            if tag_name not in TAG_CONFIG_REGISTRY:
                return False, [f"Tag '{tag_name}' not found in registry"], {}

            config_class = TAG_CONFIG_REGISTRY[tag_name]
            config_instance = config_class(self.editor)

            # Create UI widgets for proper testing
            ui_created = self.create_tag_ui_for_testing(tag_name, config_instance)
            if not ui_created:
                issues.append("Failed to create UI widgets for testing")
                # Continue with testing anyway - some tags might work without UI

            # Create test data
            test_data = self.create_test_data_for_tag(tag_name)

            # Test populate_data
            try:
                config_instance.populate_data(test_data)
            except Exception as e:
                issues.append(f"populate_data failed: {e}")
                return False, issues, {}

            # Test collect_data
            try:
                collected_data = config_instance.collect_data()
            except Exception as e:
                issues.append(f"collect_data failed: {e}")
                return False, issues, {}
            
            # Compare data integrity - exclude ability-level fields that are handled by EditorDataInterface
            ability_level_fields = {
                "version", "name", "description", "cost", "tags", "activationMode",
                "autoCostCheck"  # This is handled at ability level, not tag level
            }
            tag_specific_fields = {k: v for k, v in test_data.items()
                                 if k not in ability_level_fields}

            for field, original_value in tag_specific_fields.items():
                if field not in collected_data:
                    issues.append(f"Missing field in collected data: '{field}'")
                elif collected_data[field] != original_value:
                    issues.append(f"Data mismatch for '{field}': {original_value} → {collected_data[field]}")

            # Check for unexpected extra fields
            for field, collected_value in collected_data.items():
                if field not in tag_specific_fields:
                    issues.append(f"Unexpected extra field: '{field}' = {collected_value}")
            
            return len(issues) == 0, issues, collected_data
            
        except Exception as e:
            issues.append(f"Test failed with exception: {e}")
            return False, issues, {}

        finally:
            # Clean up test widgets
            self.cleanup_test_widgets(tag_name)

    def cleanup_test_widgets(self, tag_name: str) -> None:
        """Clean up test widgets for a specific tag."""
        try:
            if hasattr(self, '_test_widgets') and tag_name in self._test_widgets:
                widget = self._test_widgets[tag_name]
                widget.deleteLater()
                del self._test_widgets[tag_name]
        except Exception as e:
            # Ignore cleanup errors
            pass
    
    def test_all_tags(self, verbose: bool = False) -> Dict[str, Any]:
        """Test all tag configurations and return results."""
        if not self.setup_editor():
            return {"error": "Failed to setup editor"}
        
        results = {
            "total_tags": len(TAG_CONFIG_REGISTRY),
            "perfect_tags": 0,
            "problematic_tags": 0,
            "tag_results": {}
        }
        
        print(f"\n🔧 Testing {len(TAG_CONFIG_REGISTRY)} Tag Configurations")
        print("=" * 60)
        
        for tag_name in sorted(TAG_CONFIG_REGISTRY.keys()):
            print(f"\n🏷️ Testing {tag_name}")
            
            success, issues, collected_data = self.test_tag_configuration(tag_name)
            
            if success:
                print(f"  ✅ Perfect data integrity")
                results["perfect_tags"] += 1
            else:
                print(f"  ⚠️ {len(issues)} data integrity issues:")
                for issue in issues[:5]:  # Show first 5 issues
                    print(f"    - {issue}")
                if len(issues) > 5:
                    print(f"    ... and {len(issues) - 5} more issues")
                results["problematic_tags"] += 1
            
            results["tag_results"][tag_name] = {
                "success": success,
                "issues": issues,
                "collected_data": collected_data if verbose else {}
            }
        
        return results
    
    def test_specific_tag(self, tag_name: str) -> None:
        """Test a specific tag configuration with detailed output."""
        if not self.setup_editor():
            return

        print(f"\n🔍 Detailed Test for '{tag_name}' Tag Configuration")
        print("=" * 60)

        success, issues, collected_data = self.test_tag_configuration(tag_name)

        if success:
            print("✅ Perfect data integrity!")
            print(f"📊 Collected data: {json.dumps(collected_data, indent=2)}")
        else:
            print(f"⚠️ Found {len(issues)} data integrity issues:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")

            print(f"\n📊 Collected data: {json.dumps(collected_data, indent=2)}")

        # Clean up any remaining test widgets
        if hasattr(self, '_test_widgets'):
            for widget_name, widget in list(self._test_widgets.items()):
                try:
                    widget.deleteLater()
                except:
                    pass
            self._test_widgets.clear()


def main():
    """Main entry point for the tag data integrity tester."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test tag configuration data integrity")
    parser.add_argument("--tag", help="Test specific tag only")
    parser.add_argument("--verbose", action="store_true", help="Show detailed output")
    args = parser.parse_args()
    
    tester = TagDataIntegrityTester()
    
    if args.tag:
        tester.test_specific_tag(args.tag)
    else:
        results = tester.test_all_tags(args.verbose)
        
        if "error" in results:
            print(f"❌ {results['error']}")
            return
        
        print(f"\n📊 Summary:")
        print(f"  ✅ Perfect integrity: {results['perfect_tags']}/{results['total_tags']}")
        print(f"  ⚠️ Need fixes: {results['problematic_tags']}/{results['total_tags']}")
        print(f"  📈 Success rate: {results['perfect_tags']/results['total_tags']*100:.1f}%")
        
        if results['problematic_tags'] > 0:
            print(f"\n🔧 Tags needing fixes:")
            for tag_name, result in results['tag_results'].items():
                if not result['success']:
                    print(f"  - {tag_name}: {len(result['issues'])} issues")


if __name__ == "__main__":
    main()
