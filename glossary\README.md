# Adventure Chess Glossary Documentation

This folder contains versioned documentation for the Adventure Chess piece and ability editors.

## Version Overview

### v1.0.4 (Current) - File Structure Reorganization & Comprehensive Updates
- **File Structure Reorganization**: Logical folder structure with dialogs/, editors/, managers/, core/, ui/, utils/
- **Range Editor Integration**: Custom range patterns for carryPiece, passThrough, and other abilities
- **Dialog Integration**: Piece selector and ability selector dialogs fully integrated
- **Revival Configuration Enhanced**: Proper indentation and max pieces per turn spinner
- **Uncheck Functionality**: Ability to uncheck abilities from configuration tab
- **Comprehensive Documentation**: All potential updates integrated and implementation status tracked

### v1.0.3 - Canonical Abilities & Production Ready
- **Canonical Compliance**: All 28 canonical abilities documented and verified
- **Backend Cleanup**: Removed deprecated abilities (preventAbility, revealTiles)
- **Centralized Configuration**: Updated cost calculation and validation systems
- **Pattern Editor Improvements**: Enhanced legend clicking and layout consolidation
- **Production Ready**: Clean foundation for game logic implementation

### v1.0.2 - Comprehensive Reference
- **Multiple Indexes**: Quick reference, backend developer, tooltip reference
- **Complete Coverage**: All ability tags and configuration options documented
- **Technical Details**: Data structures, validation rules, best practices
- **Troubleshooting**: Common issues and solutions
- **Backend Focus**: Detailed information for developers implementing game logic

### v1.0.1 - Enhanced Editors
- **Documented Improvements**: Range and pattern editor enhancements
- **Quick Patterns**: Chess piece buttons for instant range setting
- **5-Color System**: Pattern editor with action and any types
- **UI Improvements**: Selectable patterns, better synchronization

### v1.0.0 - Initial Release
- **Basic Documentation**: Core piece and ability editor functionality
- **Tag System**: Initial ability tag descriptions
- **File Management**: Save/load system documentation

## Usage Guidelines

### For End Users
- **Start with**: Quick Reference Index in v1.0.2
- **Pattern Help**: Pattern & Range Editors section
- **Tooltips**: Tooltip Reference Index for UI guidance

### For Developers
- **Primary Resource**: Backend Developer Index in v1.0.2
- **Data Structures**: Complete object structure reference
- **Configuration**: All available options and their effects
- **Implementation**: Technical details for game logic integration

### For Documentation Updates
- **Version Naming**: Use semantic versioning (major.minor.patch)
- **Change Documentation**: Always document improvements in new versions
- **Backward Compatibility**: Maintain links to previous versions
- **Index Updates**: Keep all indexes current with actual editor capabilities

### Creating New Glossary Versions - AI/User Guide

#### **Pre-Creation Checklist**
1. **Read Current Version**: Thoroughly review the latest glossary for structure and content
2. **Identify Changes**: Note any "!!" comments indicating needed updates or refactors
3. **Test Application**: Verify all features work as documented
4. **Check Canonical Compliance**: Ensure all abilities match the canonical ABILITY_TAGS in config.py

#### **Content Requirements for New Versions**
1. **Multiple Indexes**: Always include Quick Reference, Backend Developer, and Tooltip Reference indexes
2. **Canonical Abilities**: Document all 28 canonical abilities with Function, Data, Options, Behavior, and Interactions
3. **UI Function Documentation**: In Data field, specify UI function used (range_editor, pattern_editor, custom_widget, spinner, dropdown, checkbox)
4. **Configuration Hierarchy**: Use indentation to show options revealed by parent fields
5. **OR Mechanics**: Mark mutually exclusive options with * prefix
6. **Configuration Accuracy**: Verify all configuration options match actual UI elements
7. **Remove Deprecated**: Clean out any "!!" marked items that have been fixed
8. **Add New Features**: Document any new functionality or improvements
9. **Potential Updates**: Include comprehensive UI and Backend perspective updates section

#### **Structure Guidelines**
- **Table of Contents**: Always include comprehensive navigation
- **Consistent Formatting**: Use same heading levels and markdown structure
- **Code Examples**: Include JSON structure examples for data formats
- **Visual Clarity**: Use emojis and formatting for easy scanning
- **Cross-References**: Link related sections together

#### **Documentation Format Standards**
- **UI Function Format**: `fieldName` (ui_function: options) - e.g., `areaShape` (dropdown: Circle/Square/Cross/Line/Custom)
- **Indentation Rules**: Use 2-space indentation for revealed/dependent options
- **OR Mechanics**: Use * prefix for mutually exclusive options - e.g., *`delayTurns` OR *`delayActions`
- **Conditional Options**: Show conditions in parentheses - e.g., `customPattern` (when areaShape=Custom)
- **UI Function Types**:
  - `range_editor`: 8x8 grid for range patterns
  - `pattern_editor`: 8x8 grid for movement/action patterns
  - `custom_widget`: Complex UI components (piece lists, etc.)
  - `spinner`: Numeric input with min/max values
  - `dropdown`: Selection from predefined options
  - `checkbox`: Boolean true/false options

#### **Quality Assurance**
- **No Duplicates**: Ensure no duplicate information between sections
- **Accurate Data**: All configuration options must match actual application behavior
- **UI Function Accuracy**: Verify all UI function types match actual implementation
- **Indentation Consistency**: Check that revealed options are properly indented
- **OR Mechanics Verification**: Confirm mutually exclusive options are marked with *
- **Complete Coverage**: Every UI element should have documentation
- **Potential Updates**: Include both UI and Backend perspective future enhancements
- **Version History**: Always include what changed from previous version

## File Structure

```
glossary/
├── README.md                           # This file
├── Adventure_Chess_Glossary_v1-0-0.md  # Initial release
├── Adventure_Chess_Glossary_v1-0-1.md  # Enhanced editors
├── Adventure_Chess_Glossary_v1-0-2.md  # Comprehensive reference
├── Adventure_Chess_Glossary_v1-0-3.md  # Canonical abilities & production ready
└── Adventure_Chess_Glossary_v1-0-4.md  # File structure reorganization & comprehensive updates (current)
```

## Contributing

When updating documentation:

1. **Create New Version**: Don't overwrite existing versions
2. **Document Changes**: Include version history section
3. **Update Indexes**: Ensure all indexes reflect current capabilities
4. **Validate Content**: Verify all information matches actual editor behavior
5. **Cross-Reference**: Link related sections for easy navigation

## Quick Links

- [Latest Version (v1.0.4)](Adventure_Chess_Glossary_v1-0-4.md)
- [Backend Developer Index](Adventure_Chess_Glossary_v1-0-4.md#backend-developer-index)
- [Quick Reference](Adventure_Chess_Glossary_v1-0-4.md#quick-reference-index)
- [Tooltip Reference](Adventure_Chess_Glossary_v1-0-4.md#tooltip-reference-index)
- [Canonical Abilities Reference](Adventure_Chess_Glossary_v1-0-4.md#canonical-abilities-reference)
- [File Management](Adventure_Chess_Glossary_v1-0-4.md#file-management)
